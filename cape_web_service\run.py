#!/usr/bin/env python3
"""
CAPEv2 Web Service Runner
Production-ready runner with better configuration
"""

import os
import sys
from app import app

if __name__ == '__main__':
    # Configuration
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    print("🚀 Starting CAPEv2 Web Service...")
    print(f"📡 CAPE URL: {os.environ.get('CAPE_URL', 'http://localhost:8000')}")
    print(f"🌐 Server: http://{host}:{port}")
    print(f"🔧 Debug mode: {debug}")
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down CAPEv2 Web Service...")
        sys.exit(0)
