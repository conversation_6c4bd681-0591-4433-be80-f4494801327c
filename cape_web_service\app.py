#!/usr/bin/env python3
"""
CAPEv2 Web Service
A standalone web interface for file analysis using CAPEv2
"""

import os
import sys
import json
import tempfile
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
import threading
import time

# Add the parent directory to path to import cape_simple_service
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from cape_simple_service import CAPEAnalysisService

app = Flask(__name__)
app.secret_key = 'cape_web_service_secret_key_change_this'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# Configuration
CAPE_URL = os.environ.get('CAPE_URL', 'http://localhost:8000')
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'exe', 'dll', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', '7z', 'jar', 'apk', 'bin', 'bat', 'cmd', 'ps1', 'vbs', 'js', 'html', 'htm'}

# Ensure upload folder exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Global storage for analysis results
analysis_results = {}
analysis_status = {}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def analyze_file_async(web_task_id, file_path, options, verbose=False):
    """Analyze file asynchronously"""
    try:
        analysis_status[web_task_id] = {'status': 'running', 'message': 'Analysis in progress...', 'cape_task_id': None}

        service = CAPEAnalysisService(CAPE_URL, verbose=verbose)
        result = service.analyze_file(file_path, options)

        if result['error']:
            analysis_status[web_task_id] = {
                'status': 'error',
                'message': result['message'],
                'cape_task_id': result.get('task_id')
            }
        else:
            analysis_status[web_task_id] = {
                'status': 'completed',
                'message': 'Analysis completed successfully',
                'cape_task_id': result['task_id']
            }
            analysis_results[web_task_id] = result

    except Exception as e:
        analysis_status[web_task_id] = {'status': 'error', 'message': f'Analysis failed: {str(e)}', 'cape_task_id': None}
    finally:
        # Clean up uploaded file
        try:
            os.unlink(file_path)
        except:
            pass

@app.route('/')
def index():
    """Main page with upload form"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and start analysis"""
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(request.url)
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        
        # Save file temporarily
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f"_{filename}")
        file.save(temp_file.name)
        temp_file.close()
        
        # Get analysis options
        analysis_mode = request.form.get('analysis_mode', 'basic')
        options = {}
        
        if analysis_mode == 'advanced':
            # Parse advanced options
            if request.form.get('machine'):
                options['machine'] = request.form.get('machine')
            if request.form.get('package'):
                options['package'] = request.form.get('package')
            if request.form.get('timeout'):
                options['timeout'] = request.form.get('timeout')
            if request.form.get('priority'):
                options['priority'] = request.form.get('priority')
                
            # Analysis options
            if request.form.get('process_memory'):
                options['procmemdump'] = '1'
            if request.form.get('import_reconstruction'):
                options['import_reconstruction'] = '1'
            if request.form.get('unpacker'):
                options['unpacker'] = '2'
            if request.form.get('norefer'):
                options['norefer'] = '1'
            if request.form.get('no_iat'):
                options['no-iat'] = '1'
            if request.form.get('free'):
                options['free'] = 'yes'
            if request.form.get('nohuman'):
                options['nohuman'] = 'yes'
            if request.form.get('tor'):
                options['tor'] = 'yes'
                
            # Custom options
            custom_options = request.form.get('custom_options', '').strip()
            if custom_options:
                for opt in custom_options.split(','):
                    if '=' in opt:
                        key, value = opt.split('=', 1)
                        options[key.strip()] = value.strip()
        
        # Generate web task ID
        web_task_id = f"web_{int(time.time())}_{hash(filename) % 10000}"

        # Start analysis in background
        verbose = request.form.get('verbose', '0') == '1'
        thread = threading.Thread(
            target=analyze_file_async,
            args=(web_task_id, temp_file.name, options, verbose)
        )
        thread.daemon = True
        thread.start()

        analysis_status[web_task_id] = {'status': 'pending', 'message': 'Analysis queued...', 'cape_task_id': None}

        return redirect(url_for('analysis_status_page', task_id=web_task_id))
    else:
        flash('Invalid file type')
        return redirect(request.url)

@app.route('/status/<task_id>')
def analysis_status_page(task_id):
    """Show analysis status page"""
    status = analysis_status.get(task_id, {'status': 'not_found', 'message': 'Task not found'})
    result = analysis_results.get(task_id)
    
    return render_template('status.html', task_id=task_id, status=status, result=result)

@app.route('/api/status/<task_id>')
def analysis_status_api(task_id):
    """API endpoint for checking analysis status"""
    status = analysis_status.get(task_id, {'status': 'not_found', 'message': 'Task not found'})

    # If we have a CAPE task ID and status is running, check real status from CAPE
    if status.get('cape_task_id') and status['status'] == 'running':
        try:
            service = CAPEAnalysisService(CAPE_URL, verbose=False)
            cape_status = service.check_status(status['cape_task_id'])

            if cape_status:
                # Update status based on CAPE status
                if cape_status == 'reported':
                    status['status'] = 'completed'
                    status['message'] = 'Analysis completed and reported'
                elif cape_status in ['failed_analysis', 'failed_processing', 'failed_reporting']:
                    status['status'] = 'error'
                    status['message'] = f'Analysis failed with status: {cape_status}'
                elif cape_status in ['pending', 'running', 'processing']:
                    status['message'] = f'Analysis {cape_status}...'

                # Update stored status
                analysis_status[task_id] = status
        except Exception as e:
            print(f"Error checking CAPE status: {e}")

    return jsonify(status)

@app.route('/report/<task_id>')
def view_report(task_id):
    """View analysis report"""
    result = analysis_results.get(task_id)
    if not result:
        flash('Report not found')
        return redirect(url_for('index'))
    
    return render_template('report.html', task_id=task_id, result=result)

@app.route('/api/report/<task_id>')
def get_report_api(task_id):
    """API endpoint for getting analysis report"""
    result = analysis_results.get(task_id)
    if not result:
        return jsonify({'error': True, 'message': 'Report not found'}), 404

    return jsonify(result)

@app.route('/api/cape/status/<int:cape_task_id>')
def cape_status_api(cape_task_id):
    """API endpoint for checking CAPE task status directly"""
    try:
        service = CAPEAnalysisService(CAPE_URL, verbose=False)
        status = service.check_status(cape_task_id)

        if status:
            return jsonify({
                'error': False,
                'cape_task_id': cape_task_id,
                'status': status,
                'message': f'CAPE task status: {status}'
            })
        else:
            return jsonify({
                'error': True,
                'cape_task_id': cape_task_id,
                'message': 'Could not retrieve status from CAPE'
            }), 404

    except Exception as e:
        return jsonify({
            'error': True,
            'cape_task_id': cape_task_id,
            'message': f'Error checking CAPE status: {str(e)}'
        }), 500

@app.route('/api/cape/report/<int:cape_task_id>')
def cape_report_api(cape_task_id):
    """API endpoint for getting CAPE report directly"""
    try:
        service = CAPEAnalysisService(CAPE_URL, verbose=False)
        report = service.get_report(cape_task_id, 'json')

        if report:
            return jsonify({
                'error': False,
                'cape_task_id': cape_task_id,
                'report': report
            })
        else:
            return jsonify({
                'error': True,
                'cape_task_id': cape_task_id,
                'message': 'Report not found'
            }), 404

    except Exception as e:
        return jsonify({
            'error': True,
            'cape_task_id': cape_task_id,
            'message': f'Error getting CAPE report: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 Starting CAPEv2 Web Service...")
    print(f"📡 CAPE URL: {CAPE_URL}")
    print(f"📁 Upload folder: {UPLOAD_FOLDER}")
    print("🌐 Access at: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
