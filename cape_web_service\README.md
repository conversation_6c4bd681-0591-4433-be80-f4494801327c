# CAPEv2 Web Service

A standalone web interface for file analysis using CAPEv2.

## Features

- **Simple File Upload**: Drag & drop or browse to upload files
- **Two Analysis Modes**:
  - **Basic**: Uses default optimized settings (procmemdump=1, import_reconstruction=1, unpacker=2, norefer=1, no-iat=1)
  - **Advanced**: Full customization like CAPEv2 web interface
- **Real-time Status**: Live updates during analysis
- **Rich Reports**: Visual report display similar to CAPEv2
- **API Endpoints**: RESTful API for automation

## Installation

1. **Install dependencies**:
```bash
pip install -r requirements.txt
```

2. **Set CAPE URL** (optional):
```bash
export CAPE_URL=http://your-cape-server:8000
```

3. **Run the service**:
```bash
python app.py
```

4. **Access the web interface**:
```
http://localhost:5000
```

## Usage

### Web Interface

1. **Upload File**: 
   - Drag & drop or browse to select file
   - Choose Basic or Advanced analysis mode
   - Click "Start Analysis"

2. **Monitor Progress**:
   - Real-time status updates
   - Auto-refresh every 5 seconds
   - Progress bar showing current stage

3. **View Results**:
   - Rich HTML report with signatures, network activity, behavior
   - Download raw JSON report
   - Screenshots and file information

### API Endpoints

- `POST /upload` - Upload file for analysis
- `GET /api/status/<task_id>` - Check analysis status
- `GET /api/report/<task_id>` - Get analysis report (JSON)
- `GET /status/<task_id>` - Status page (HTML)
- `GET /report/<task_id>` - Report page (HTML)

### Analysis Modes

#### Basic Mode (Default)
Automatically applies optimized settings:
- `procmemdump=1` - Process memory dump
- `import_reconstruction=1` - Import table reconstruction  
- `unpacker=2` - Advanced unpacker
- `norefer=1` - Disable referrer
- `no-iat=1` - Disable IAT reconstruction

#### Advanced Mode
Full control over analysis options:
- VM selection
- Package selection
- Timeout and priority
- Individual analysis options
- Custom options string

## Configuration

### Environment Variables

- `CAPE_URL` - CAPEv2 server URL (default: http://localhost:8000)

### File Upload

- **Max file size**: 100MB
- **Supported formats**: EXE, DLL, PDF, DOC, XLS, PPT, ZIP, APK, etc.
- **Upload folder**: `uploads/` (auto-created)

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Browser   │───▶│  Flask Web App   │───▶│  cape_simple_   │
│                 │    │                  │    │    service.py   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  File Storage    │    │   CAPEv2 API    │
                       │   (uploads/)     │    │                 │
                       └──────────────────┘    └─────────────────┘
```

## API Examples

### Upload File
```bash
curl -X POST -F "file=@malware.exe" -F "analysis_mode=basic" \
  http://localhost:5000/upload
```

### Check Status
```bash
curl http://localhost:5000/api/status/web_1234567890_1234
```

### Get Report
```bash
curl http://localhost:5000/api/report/web_1234567890_1234
```

## Security Notes

- Change the Flask secret key in production
- Consider adding authentication for production use
- File uploads are temporarily stored and cleaned up after analysis
- No persistent storage of analysis results (stored in memory)

## Troubleshooting

1. **Connection Error**: Check CAPE_URL environment variable
2. **File Upload Fails**: Check file size and format
3. **Analysis Stuck**: Check CAPEv2 server status
4. **Memory Issues**: Restart service to clear analysis_results cache
