#!/usr/bin/env python3
"""
CAPEv2 Web Service
A standalone web interface for file analysis using CAPEv2
"""

import os
import sys
import json
import tempfile
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
import threading
import time

# Add the parent directory to path to import cape_simple_service
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from cape_simple_service import CAPEAnalysisService

app = Flask(__name__)
app.secret_key = 'cape_web_service_secret_key_change_this'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# Configuration
CAPE_URL = os.environ.get('CAPE_URL', 'http://localhost:8000')
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'exe', 'dll', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', '7z', 'jar', 'apk', 'bin', 'bat', 'cmd', 'ps1', 'vbs', 'js', 'html', 'htm'}

# Ensure upload folder exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Global storage for analysis results
analysis_results = {}
analysis_status = {}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def analyze_file_async(task_id, file_path, options, verbose=False):
    """Analyze file asynchronously using cape_simple_service logic"""
    try:
        analysis_status[task_id] = {'status': 'pending', 'message': 'Analysis queued...'}

        # Use CAPEAnalysisService like cape_simple_service.py
        service = CAPEAnalysisService(CAPE_URL, verbose=verbose)

        # Update status to running
        analysis_status[task_id] = {'status': 'running', 'message': 'Analysis in progress...'}

        # Run analysis (this will handle all the logic from cape_simple_service.py)
        result = service.analyze_file(file_path, options)

        if result['error']:
            analysis_status[task_id] = {'status': 'error', 'message': result['message']}
        else:
            analysis_status[task_id] = {'status': 'completed', 'message': 'Analysis completed successfully'}
            analysis_results[task_id] = result

    except Exception as e:
        analysis_status[task_id] = {'status': 'error', 'message': f'Analysis failed: {str(e)}'}
    finally:
        # Clean up uploaded file
        try:
            os.unlink(file_path)
        except:
            pass

@app.route('/')
def index():
    """Main page with upload form"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and start analysis"""
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(request.url)
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        
        # Save file temporarily
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f"_{filename}")
        file.save(temp_file.name)
        temp_file.close()
        
        # Get analysis options
        analysis_mode = request.form.get('analysis_mode', 'basic')
        options = {}
        
        if analysis_mode == 'advanced':
            # Parse advanced options
            if request.form.get('machine'):
                options['machine'] = request.form.get('machine')
            if request.form.get('package'):
                options['package'] = request.form.get('package')
            if request.form.get('timeout'):
                options['timeout'] = request.form.get('timeout')
            if request.form.get('priority'):
                options['priority'] = request.form.get('priority')
                
            # Analysis options
            if request.form.get('process_memory'):
                options['procmemdump'] = '1'
            if request.form.get('import_reconstruction'):
                options['import_reconstruction'] = '1'
            if request.form.get('unpacker'):
                options['unpacker'] = '2'
            if request.form.get('norefer'):
                options['norefer'] = '1'
            if request.form.get('no_iat'):
                options['no-iat'] = '1'
            if request.form.get('free'):
                options['free'] = 'yes'
            if request.form.get('nohuman'):
                options['nohuman'] = 'yes'
            if request.form.get('tor'):
                options['tor'] = 'yes'
                
            # Custom options
            custom_options = request.form.get('custom_options', '').strip()
            if custom_options:
                for opt in custom_options.split(','):
                    if '=' in opt:
                        key, value = opt.split('=', 1)
                        options[key.strip()] = value.strip()
        
        # Generate task ID
        task_id = f"task_{int(time.time())}_{hash(filename) % 10000}"

        # Start analysis in background
        verbose = request.form.get('verbose', '0') == '1'
        thread = threading.Thread(
            target=analyze_file_async,
            args=(task_id, temp_file.name, options, verbose)
        )
        thread.daemon = True
        thread.start()

        # Initial status
        analysis_status[task_id] = {'status': 'pending', 'message': 'Analysis queued...'}

        return redirect(url_for('analysis_status_page', task_id=task_id))
    else:
        flash('Invalid file type')
        return redirect(request.url)

@app.route('/status/<task_id>')
def analysis_status_page(task_id):
    """Show analysis status page"""
    status = analysis_status.get(task_id, {'status': 'not_found', 'message': 'Task not found'})
    result = analysis_results.get(task_id)
    
    return render_template('status.html', task_id=task_id, status=status, result=result)

@app.route('/api/status/<task_id>')
def analysis_status_api(task_id):
    """API endpoint for checking analysis status"""
    status = analysis_status.get(task_id, {'status': 'not_found', 'message': 'Task not found'})
    return jsonify(status)

@app.route('/report/<task_id>')
def view_report(task_id):
    """View analysis report"""
    result = analysis_results.get(task_id)
    if not result:
        flash('Report not found')
        return redirect(url_for('index'))
    
    return render_template('report.html', task_id=task_id, result=result)

@app.route('/api/report/<task_id>')
def get_report_api(task_id):
    """API endpoint for getting analysis report"""
    result = analysis_results.get(task_id)
    if not result:
        return jsonify({'error': True, 'message': 'Report not found'}), 404

    return jsonify(result)



if __name__ == '__main__':
    print("🚀 Starting CAPEv2 Web Service...")
    print(f"📡 CAPE URL: {CAPE_URL}")
    print(f"📁 Upload folder: {UPLOAD_FOLDER}")
    print("🌐 Access at: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
