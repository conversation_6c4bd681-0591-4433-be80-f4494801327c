{% extends "base.html" %}

{% block title %}Analysis Status - CAPEv2 Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-chart-line"></i> Analysis Status</h4>
                <p class="mb-0 text-muted">Web Task ID: {{ task_id }}</p>
                {% if status.cape_task_id %}
                <p class="mb-0 text-muted">CAPE Task ID: {{ status.cape_task_id }}</p>
                {% endif %}
            </div>
            <div class="card-body">
                <!-- Status Display -->
                <div class="text-center mb-4">
                    <div id="statusIcon" class="mb-3">
                        {% if status.status == 'pending' %}
                            <i class="fas fa-clock fa-3x status-pending"></i>
                        {% elif status.status == 'running' %}
                            <i class="fas fa-spinner fa-spin fa-3x status-running"></i>
                        {% elif status.status == 'completed' %}
                            <i class="fas fa-check-circle fa-3x status-completed"></i>
                        {% elif status.status == 'error' %}
                            <i class="fas fa-exclamation-triangle fa-3x status-error"></i>
                        {% else %}
                            <i class="fas fa-question-circle fa-3x text-muted"></i>
                        {% endif %}
                    </div>
                    <h5 id="statusText">
                        {% if status.status == 'pending' %}
                            Analysis Pending
                        {% elif status.status == 'running' %}
                            Analysis Running
                        {% elif status.status == 'completed' %}
                            Analysis Completed
                        {% elif status.status == 'error' %}
                            Analysis Failed
                        {% else %}
                            Unknown Status
                        {% endif %}
                    </h5>
                    <p id="statusMessage" class="text-muted">{{ status.message }}</p>
                </div>

                <!-- Progress Bar -->
                <div class="progress mb-4" style="height: 25px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped" role="progressbar" 
                         style="width: {% if status.status == 'pending' %}25%{% elif status.status == 'running' %}75%{% elif status.status == 'completed' %}100%{% else %}0%{% endif %}">
                        <span id="progressText">
                            {% if status.status == 'pending' %}
                                Queued
                            {% elif status.status == 'running' %}
                                Analyzing...
                            {% elif status.status == 'completed' %}
                                Complete
                            {% elif status.status == 'error' %}
                                Failed
                            {% else %}
                                Unknown
                            {% endif %}
                        </span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center">
                    {% if status.status == 'completed' and result %}
                        <a href="{{ url_for('view_report', task_id=task_id) }}" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-file-alt"></i> View Report
                        </a>
                        <a href="{{ url_for('get_report_api', task_id=task_id) }}" class="btn btn-outline-primary" target="_blank">
                            <i class="fas fa-download"></i> Download JSON
                        </a>
                    {% elif status.status in ['pending', 'running'] %}
                        <button id="refreshBtn" class="btn btn-primary" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i> Refresh Status
                        </button>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i> 
                                This page will auto-refresh every 5 seconds
                            </small>
                        </div>
                    {% elif status.status == 'error' %}
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Try Another File
                        </a>
                    {% endif %}
                </div>

                <!-- Analysis Details (if completed) -->
                {% if status.status == 'completed' and result %}
                <div class="mt-5">
                    <h6><i class="fas fa-info-circle"></i> Analysis Summary</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Task ID:</strong></td>
                                    <td>{{ result.task_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>File Hash (MD5):</strong></td>
                                    <td><code>{{ result.file_hash }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Analysis Type:</strong></td>
                                    <td>
                                        {% if result.was_cached %}
                                            <span class="badge bg-info">Cached Result</span>
                                        {% else %}
                                            <span class="badge bg-success">Fresh Analysis</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            {% if result.report and result.report.info %}
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>CAPE Task ID:</strong></td>
                                    <td>{{ result.task_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Duration:</strong></td>
                                    <td>{{ result.report.info.duration }} seconds</td>
                                </tr>
                                <tr>
                                    <td><strong>Score:</strong></td>
                                    <td>
                                        {% if result.report.info.score %}
                                            <span class="badge {% if result.report.info.score > 7 %}bg-danger{% elif result.report.info.score > 4 %}bg-warning{% else %}bg-success{% endif %}">
                                                {{ result.report.info.score }}/10
                                            </span>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                            {% elif status.cape_task_id %}
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>CAPE Task ID:</strong></td>
                                    <td>{{ status.cape_task_id }}</td>
                                </tr>
                            </table>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let refreshInterval;

function refreshStatus() {
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshBtn.disabled = true;
    }
    
    fetch(`/api/status/{{ task_id }}`)
        .then(response => response.json())
        .then(data => {
            updateStatusDisplay(data);
            
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Status';
                refreshBtn.disabled = false;
            }
            
            // If completed or error, reload the page to show results
            if (data.status === 'completed' || data.status === 'error') {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        })
        .catch(error => {
            console.error('Error refreshing status:', error);
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Status';
                refreshBtn.disabled = false;
            }
        });
}

function updateStatusDisplay(status) {
    const statusIcon = document.getElementById('statusIcon');
    const statusText = document.getElementById('statusText');
    const statusMessage = document.getElementById('statusMessage');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    // Update icon
    let iconClass = 'fas fa-question-circle fa-3x text-muted';
    let textContent = 'Unknown Status';
    let progressWidth = '0%';
    let progressContent = 'Unknown';
    
    switch(status.status) {
        case 'pending':
            iconClass = 'fas fa-clock fa-3x status-pending';
            textContent = 'Analysis Pending';
            progressWidth = '25%';
            progressContent = 'Queued';
            break;
        case 'running':
            iconClass = 'fas fa-spinner fa-spin fa-3x status-running';
            textContent = 'Analysis Running';
            progressWidth = '75%';
            progressContent = 'Analyzing...';
            break;
        case 'completed':
            iconClass = 'fas fa-check-circle fa-3x status-completed';
            textContent = 'Analysis Completed';
            progressWidth = '100%';
            progressContent = 'Complete';
            break;
        case 'error':
            iconClass = 'fas fa-exclamation-triangle fa-3x status-error';
            textContent = 'Analysis Failed';
            progressWidth = '100%';
            progressContent = 'Failed';
            progressBar.classList.add('bg-danger');
            break;
    }
    
    statusIcon.innerHTML = `<i class="${iconClass}"></i>`;
    statusText.textContent = textContent;
    statusMessage.textContent = status.message;
    progressBar.style.width = progressWidth;
    progressText.textContent = progressContent;
}

// Auto-refresh for pending/running status
{% if status.status in ['pending', 'running'] %}
refreshInterval = setInterval(refreshStatus, 5000);

// Stop auto-refresh when page is hidden
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    } else {
        // Resume auto-refresh when page becomes visible again
        if ('{{ status.status }}' === 'pending' || '{{ status.status }}' === 'running') {
            refreshInterval = setInterval(refreshStatus, 5000);
        }
    }
});
{% endif %}
</script>
{% endblock %}
