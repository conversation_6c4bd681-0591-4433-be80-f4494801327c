{% extends "base.html" %}

{% block title %}Analysis Report - CAPEv2 Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4><i class="fas fa-file-alt"></i> Analysis Report</h4>
                    <p class="mb-0 text-muted">Task ID: {{ task_id }}</p>
                </div>
                <div>
                    <a href="{{ url_for('get_report_api', task_id=task_id) }}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-download"></i> Download JSON
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-upload"></i> New Analysis
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if result and result.report %}
                    {% set report = result.report %}
                    
                    <!-- Summary Section -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle"></i> File Information</h5>
                            <table class="table table-sm">
                                {% if report.target and report.target.file %}
                                <tr>
                                    <td><strong>Filename:</strong></td>
                                    <td>{{ report.target.file.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Size:</strong></td>
                                    <td>{{ "{:,}".format(report.target.file.size) }} bytes</td>
                                </tr>
                                <tr>
                                    <td><strong>Type:</strong></td>
                                    <td>{{ report.target.file.type }}</td>
                                </tr>
                                <tr>
                                    <td><strong>MD5:</strong></td>
                                    <td><code>{{ report.target.file.md5 }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>SHA1:</strong></td>
                                    <td><code>{{ report.target.file.sha1 }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>SHA256:</strong></td>
                                    <td><code>{{ report.target.file.sha256 }}</code></td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-chart-line"></i> Analysis Information</h5>
                            <table class="table table-sm">
                                {% if report.info %}
                                <tr>
                                    <td><strong>Analysis ID:</strong></td>
                                    <td>{{ report.info.id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Started:</strong></td>
                                    <td>{{ report.info.started }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Duration:</strong></td>
                                    <td>{{ report.info.duration }} seconds</td>
                                </tr>
                                <tr>
                                    <td><strong>Score:</strong></td>
                                    <td>
                                        {% if report.info.score %}
                                            <span class="badge {% if report.info.score > 7 %}bg-danger{% elif report.info.score > 4 %}bg-warning{% else %}bg-success{% endif %} fs-6">
                                                {{ report.info.score }}/10
                                            </span>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Analysis Type:</strong></td>
                                    <td>
                                        {% if result.was_cached %}
                                            <span class="badge bg-info">Cached Result</span>
                                        {% else %}
                                            <span class="badge bg-success">Fresh Analysis</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- Signatures Section -->
                    {% if report.signatures %}
                    <div class="mb-4">
                        <h5><i class="fas fa-exclamation-triangle"></i> Signatures ({{ report.signatures|length }})</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Severity</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sig in report.signatures[:10] %}
                                    <tr>
                                        <td>
                                            <span class="badge {% if sig.severity == 3 %}bg-danger{% elif sig.severity == 2 %}bg-warning{% else %}bg-info{% endif %}">
                                                {{ sig.severity }}
                                            </span>
                                        </td>
                                        <td><strong>{{ sig.name }}</strong></td>
                                        <td>{{ sig.description }}</td>
                                    </tr>
                                    {% endfor %}
                                    {% if report.signatures|length > 10 %}
                                    <tr>
                                        <td colspan="3" class="text-center text-muted">
                                            ... and {{ report.signatures|length - 10 }} more signatures
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Network Activity -->
                    {% if report.network %}
                    <div class="mb-4">
                        <h5><i class="fas fa-network-wired"></i> Network Activity</h5>
                        <div class="row">
                            {% if report.network.hosts %}
                            <div class="col-md-6">
                                <h6>Contacted Hosts ({{ report.network.hosts|length }})</h6>
                                <div class="list-group" style="max-height: 300px; overflow-y: auto;">
                                    {% for host in report.network.hosts[:10] %}
                                    <div class="list-group-item">
                                        <strong>{{ host.ip }}</strong>
                                        {% if host.country_name %}
                                            <small class="text-muted">({{ host.country_name }})</small>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                            {% if report.network.domains %}
                            <div class="col-md-6">
                                <h6>DNS Requests ({{ report.network.domains|length }})</h6>
                                <div class="list-group" style="max-height: 300px; overflow-y: auto;">
                                    {% for domain in report.network.domains[:10] %}
                                    <div class="list-group-item">
                                        <strong>{{ domain.domain }}</strong>
                                        {% if domain.ip %}
                                            <br><small class="text-muted">→ {{ domain.ip }}</small>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Behavior Summary -->
                    {% if report.behavior and report.behavior.summary %}
                    <div class="mb-4">
                        <h5><i class="fas fa-cogs"></i> Behavior Summary</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-primary">{{ report.behavior.summary.files|length if report.behavior.summary.files else 0 }}</h4>
                                        <p class="card-text">Files Created</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-warning">{{ report.behavior.summary.keys|length if report.behavior.summary.keys else 0 }}</h4>
                                        <p class="card-text">Registry Keys</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-info">{{ report.behavior.summary.mutexes|length if report.behavior.summary.mutexes else 0 }}</h4>
                                        <p class="card-text">Mutexes</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-success">{{ report.behavior.processes|length if report.behavior.processes else 0 }}</h4>
                                        <p class="card-text">Processes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Screenshots -->
                    {% if report.screenshots %}
                    <div class="mb-4">
                        <h5><i class="fas fa-camera"></i> Screenshots ({{ report.screenshots|length }})</h5>
                        <div class="row">
                            {% for screenshot in report.screenshots[:6] %}
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <img src="data:image/png;base64,{{ screenshot.data }}" class="card-img-top" alt="Screenshot" style="max-height: 200px; object-fit: contain;">
                                    <div class="card-body p-2">
                                        <small class="text-muted">{{ screenshot.path }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Raw JSON Data -->
                    <div class="mb-4">
                        <h5><i class="fas fa-code"></i> Raw Data</h5>
                        <div class="accordion" id="rawDataAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="rawDataHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rawDataCollapse">
                                        View Raw JSON Report
                                    </button>
                                </h2>
                                <div id="rawDataCollapse" class="accordion-collapse collapse" data-bs-parent="#rawDataAccordion">
                                    <div class="accordion-body">
                                        <pre><code id="jsonData">{{ result.report | tojson(indent=2) }}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        No report data available for this analysis.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Syntax highlighting for JSON (basic)
document.addEventListener('DOMContentLoaded', function() {
    const jsonElement = document.getElementById('jsonData');
    if (jsonElement) {
        // Basic JSON syntax highlighting
        let jsonText = jsonElement.textContent;
        jsonText = jsonText.replace(/"([^"]+)":/g, '<span style="color: #0066cc;">"$1":</span>');
        jsonText = jsonText.replace(/: "([^"]+)"/g, ': <span style="color: #009900;">"$1"</span>');
        jsonText = jsonText.replace(/: (\d+)/g, ': <span style="color: #ff6600;">$1</span>');
        jsonText = jsonText.replace(/: (true|false|null)/g, ': <span style="color: #cc0066;">$1</span>');
        jsonElement.innerHTML = jsonText;
    }
});
</script>
{% endblock %}
