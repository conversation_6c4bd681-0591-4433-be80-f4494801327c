#!/usr/bin/env python3
"""
CAPEv2 Simple Analysis Service
Synchronous file analysis pipeline using existing CAPEv2 API
"""

import requests
import time
import json
import os
import sys
import hashlib
from pathlib import Path


class CAPEAnalysisService:
    def __init__(self, cape_url="http://localhost:8000", timeout=300, max_wait=1800, verbose=True):
        """
        Initialize CAPE analysis service

        Args:
            cape_url: Base URL of CAPEv2 web interface
            timeout: Analysis timeout in seconds
            max_wait: Maximum time to wait for completion
            verbose: Show detailed progress (True) or only results/errors (False)
        """
        self.cape_url = cape_url.rstrip('/')
        self.timeout = timeout
        self.max_wait = max_wait
        self.verbose = verbose
        self.session = requests.Session()

        # Default analysis options
        self.default_options = {
            'procmemdump': '1',
            'import_reconstruction': '1',
            'unpacker': '2',
            'norefer': '1',
            'no-iat': '1'
        }

    def _print(self, message, force=False):
        """Print message only if verbose mode is on or force is True"""
        if self.verbose or force:
            print(message)
    
    def calculate_file_hash(self, file_path, hash_type='md5'):
        """
        Calculate file hash

        Args:
            file_path: Path to file
            hash_type: Type of hash (md5, sha1, sha256)

        Returns:
            Hash string or None if error
        """
        try:
            hash_func = getattr(hashlib, hash_type)()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            self._print(f"✗ Error calculating {hash_type}: {e}", force=True)
            return None

    def search_existing_analysis(self, file_hash):
        """
        Search for existing analysis by file hash with report availability check

        Args:
            file_hash: MD5 hash of file

        Returns:
            task_id if found with available report, None if not found
        """
        url = f"{self.cape_url}/apiv2/tasks/search/md5/{file_hash}/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    tasks = result['data']
                    if tasks:
                        # Check tasks from most recent to oldest
                        for task in sorted(tasks, key=lambda x: x.get('id', 0), reverse=True):
                            if task.get('status') in ['completed', 'reported']:
                                task_id = task['id']
                                self._print(f"✓ Found existing analysis: Task ID {task_id}")

                                # Verify report is actually available
                                if self._check_report_availability(task_id):
                                    self._print(f"✓ Report confirmed available for Task ID {task_id}")
                                    return task_id
                                else:
                                    self._print(f"⚠️  Report not available for Task ID {task_id}, checking next...")
                                    continue
            return None
        except Exception as e:
            self._print(f"⚠️  Warning: Could not search existing analysis: {e}", force=True)
            return None

    def _check_report_availability(self, task_id):
        """
        Check if report is actually available for a task

        Args:
            task_id: Task ID to check

        Returns:
            True if report is available, False otherwise
        """
        url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/json/"

        try:
            response = self.session.get(url)
            return response.status_code == 200
        except:
            return False

    def submit_file(self, file_path, options=None):
        """
        Submit file for analysis

        Args:
            file_path: Path to file to analyze
            options: Dict of analysis options

        Returns:
            task_id or None if failed
        """
        url = f"{self.cape_url}/apiv2/tasks/create/file/"

        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f)}
                data = {
                    'timeout': self.timeout,
                    'priority': 1
                }

                # Merge default options with user options
                merged_options = self.default_options.copy()
                if options:
                    merged_options.update(options)

                # Handle analysis options
                analysis_options = []
                standard_options = ['machine', 'package', 'timeout', 'priority', 'memory', 'enforce_timeout']

                for key, value in merged_options.items():
                    if key in standard_options:
                        data[key] = value
                    else:
                        # Analysis-specific options go into options string
                        analysis_options.append(f"{key}={value}")

                # Combine analysis options into options string
                if analysis_options:
                    data['options'] = ','.join(analysis_options)
                    self._print(f"📋 Analysis options: {data['options']}")

                self._print(f"📤 Submitting with data: {data}")
                response = self.session.post(url, files=files, data=data)

                if response.status_code == 200:
                    result = response.json()
                    if not result.get('error') and result.get('data', {}).get('task_ids'):
                        task_id = result['data']['task_ids'][0]
                        self._print(f"✓ File submitted successfully. Task ID: {task_id}")
                        return task_id
                    else:
                        self._print(f"✗ Submission failed: {result.get('error_value', 'Unknown error')}", force=True)
                        return None
                else:
                    self._print(f"✗ HTTP Error {response.status_code}: {response.text}", force=True)
                    return None

        except Exception as e:
            self._print(f"✗ Error submitting file: {e}", force=True)
            return None
    
    def check_status(self, task_id):
        """Check task status"""
        url = f"{self.cape_url}/apiv2/tasks/view/{task_id}/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    # Try different possible response structures
                    status = None

                    # Try: data.task.status
                    if 'data' in result and 'task' in result['data']:
                        status = result['data']['task'].get('status')

                    # Try: data.status
                    elif 'data' in result:
                        status = result['data'].get('status')

                    # Try: task.status
                    elif 'task' in result:
                        status = result['task'].get('status')

                    # Try: status directly
                    elif 'status' in result:
                        status = result.get('status')

                    # Debug: print response structure if status not found
                    if status is None:
                        print(f"🔍 Debug - Response structure: {list(result.keys())}")
                        if 'data' in result:
                            print(f"🔍 Debug - Data keys: {list(result['data'].keys())}")

                        # Try alternative endpoint
                        return self._check_status_alternative(task_id)

                    return status
            else:
                print(f"🔍 Debug - HTTP {response.status_code}: {response.text[:200]}")
            return None
        except Exception as e:
            print(f"🔍 Debug - Exception checking status: {e}")
            return None

    def _check_status_alternative(self, task_id):
        """Alternative method to check task status"""
        # Try the status endpoint used in the original API
        url = f"{self.cape_url}/apiv2/tasks/status/{task_id}/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    status = result.get('data')
                    print(f"🔍 Alternative status check: {status}")
                    return status
        except Exception as e:
            print(f"🔍 Alternative status check failed: {e}")

        return None
    
    def wait_for_completion(self, task_id):
        """
        Wait for analysis to complete

        Args:
            task_id: Task ID to wait for

        Returns:
            True if completed, False if timeout/error
        """
        self._print(f"⏳ Waiting for analysis completion (max {self.max_wait}s)...")

        start_time = time.time()
        last_status = None
        check_count = 0

        while time.time() - start_time < self.max_wait:
            status = self.check_status(task_id)
            check_count += 1

            if status != last_status:
                self._print(f"📊 Status: {status} (check #{check_count})")
                last_status = status

            # Updated status handling for all 4 stages
            if status == 'pending':
                self._print("⏳ Task is pending...")
            elif status == 'running':
                self._print("🔄 Analysis is running...")
            elif status == 'processing':
                self._print("⚙️  Processing results...")
            elif status == 'reported':
                self._print("✓ Analysis completed and reported!")
                return True
            elif status in ['completed']:
                self._print("✓ Analysis completed!")
                # Wait a bit more for processing to complete
                time.sleep(10)
                return True
            elif status in ['failed_analysis', 'failed_processing', 'failed_reporting']:
                self._print(f"✗ Analysis failed with status: {status}", force=True)
                return False
            elif status is None:
                self._print(f"⚠️  Could not get status (check #{check_count})")
                # Try alternative method to check if report is available
                if self._check_report_availability(task_id):
                    self._print("✓ Report is available, assuming analysis completed!")
                    return True

            time.sleep(5)  # Check every 5 seconds

        print("⏰ Timeout waiting for analysis completion")
        print("🔍 Checking if report is available despite timeout...")

        # Final check - maybe the report is ready even if status check failed
        if self._check_report_availability(task_id):
            print("✓ Report found! Analysis appears to be complete.")
            return True

        return False
    
    def get_report(self, task_id, format='json'):
        """
        Get analysis report with fallback options

        Args:
            task_id: Task ID
            format: Report format ('json', 'html', 'lite')

        Returns:
            Report data or None
        """
        # Try requested format first
        url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{format}/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                if format == 'json' or format == 'lite':
                    return response.json()
                else:
                    return response.text
            elif response.status_code == 404:
                print(f"⚠️  {format.upper()} report not found, trying alternatives...")

                # Try alternative formats if requested format fails
                if format == 'htmlsummary':
                    fallback_formats = ['lite', 'json']
                elif format in ['lite', 'json']:
                    fallback_formats = ['json', 'lite', 'htmlsummary']
                else:
                    fallback_formats = ['lite', 'json', 'htmlsummary']

                for fallback_format in fallback_formats:
                    if fallback_format == format:
                        continue

                    fallback_url = f"{self.cape_url}/apiv2/tasks/get/report/{task_id}/{fallback_format}/"
                    try:
                        fallback_response = self.session.get(fallback_url)
                        if fallback_response.status_code == 200:
                            print(f"✓ Using {fallback_format.upper()} report instead")
                            if fallback_format in ['json', 'lite']:
                                return fallback_response.json()
                            else:
                                return fallback_response.text
                    except:
                        continue

                print(f"✗ No reports available for task {task_id}")
                return None
            else:
                print(f"✗ Error getting report: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"✗ Error getting report: {e}")
            return None
    
    def analyze_file(self, file_path, options=None, output_file=None, report_format='json', force_reanalyze=False):
        """
        Complete analysis pipeline: check existing → submit → wait → get results

        Args:
            file_path: Path to file to analyze
            options: Analysis options dict
            output_file: Path to save report (optional)
            report_format: Format of report ('json', 'html', 'lite')
            force_reanalyze: Force new analysis even if existing results found

        Returns:
            dict with results or error info
        """
        self._print(f"🔍 Starting analysis of: {file_path}")

        # Check if file exists
        if not os.path.exists(file_path):
            return {"error": True, "message": f"File not found: {file_path}"}

        # Calculate file hash
        self._print("🔢 Calculating file hash...")
        file_hash = self.calculate_file_hash(file_path, 'md5')
        if not file_hash:
            return {"error": True, "message": "Failed to calculate file hash"}

        self._print(f"📋 File MD5: {file_hash}")

        # Check for existing analysis (unless forced to reanalyze)
        task_id = None
        was_cached = False
        if not force_reanalyze:
            self._print("🔍 Checking for existing analysis...")
            existing_task_id = self.search_existing_analysis(file_hash)
            if existing_task_id:
                self._print("♻️  Using existing analysis results")
                task_id = existing_task_id
                was_cached = True

        # If no existing analysis found or forced reanalysis, submit new task
        if task_id is None:
            if force_reanalyze:
                self._print("🔄 Force reanalysis requested")
            else:
                self._print("🆕 No existing analysis found or reports unavailable")

            self._print("📤 Submitting file for new analysis...")
            task_id = self.submit_file(file_path, options)
            if not task_id:
                return {"error": True, "message": "Failed to submit file"}

            # Wait for completion
            if not self.wait_for_completion(task_id):
                return {"error": True, "message": "Analysis did not complete in time", "task_id": task_id}

        # Get report
        self._print("📄 Retrieving analysis report...")
        report = self.get_report(task_id, report_format)

        if report is None:
            return {"error": True, "message": "Failed to retrieve report", "task_id": task_id}

        # Save reports automatically with hash-based filenames
        self._save_reports_auto(task_id, file_hash, report, output_file, report_format)

        self._print("✅ Analysis completed successfully!", force=True)
        return {
            "error": False,
            "task_id": task_id,
            "file_hash": file_hash,
            "report": report,
            "message": "Analysis completed successfully",
            "was_cached": was_cached
        }

    def _save_reports_auto(self, task_id, file_hash, primary_report, custom_output_file, primary_format):
        """
        Automatically save JSON and HTML summary reports with hash-based filenames

        Args:
            task_id: Task ID
            file_hash: MD5 hash of the file
            primary_report: The primary report data
            custom_output_file: Custom output file if specified
            primary_format: Primary format that was requested
        """
        saved_files = []

        # Save custom output file if specified
        if custom_output_file:
            try:
                with open(custom_output_file, 'w', encoding='utf-8') as f:
                    if primary_format in ['json', 'lite']:
                        # JSON formats
                        if isinstance(primary_report, dict):
                            json.dump(primary_report, f, indent=2, ensure_ascii=False)
                        else:
                            f.write(str(primary_report))
                    else:
                        # HTML formats
                        if isinstance(primary_report, dict):
                            f.write(str(primary_report))
                        else:
                            f.write(primary_report)
                print(f"💾 Custom report saved to: {custom_output_file}")
                saved_files.append(custom_output_file)
            except Exception as e:
                print(f"⚠️  Warning: Could not save custom report: {e}")

        # Auto-save JSON and HTML summary reports with hash-based names
        formats_to_save = ['json', 'htmlsummary']

        for format_type in formats_to_save:
            try:
                # Skip if this is the primary format and we already have the data
                if format_type == primary_format and primary_report:
                    report_data = primary_report
                else:
                    # Get report in this format
                    print(f"📄 Downloading {format_type.upper()} report...")
                    report_data = self.get_report(task_id, format_type)
                    if not report_data:
                        print(f"⚠️  Could not get {format_type.upper()} report")
                        continue

                # Determine file extension and name
                if format_type == 'json':
                    filename = f"{file_hash}.json"
                elif format_type == 'htmlsummary':
                    filename = f"{file_hash}.html"
                else:
                    filename = f"{file_hash}.{format_type}"

                # Save the report with appropriate format
                with open(filename, 'w', encoding='utf-8') as f:
                    if format_type in ['json', 'lite']:
                        # JSON formats - save as JSON
                        if isinstance(report_data, dict):
                            json.dump(report_data, f, indent=2, ensure_ascii=False)
                        else:
                            f.write(str(report_data))
                    else:
                        # HTML formats - save as HTML content
                        if isinstance(report_data, dict):
                            # If we got JSON data for HTML format, convert to string
                            f.write(str(report_data))
                        else:
                            # Raw HTML content
                            f.write(report_data)

                print(f"💾 {format_type.upper()} report saved to: {filename}")
                saved_files.append(filename)

            except Exception as e:
                print(f"⚠️  Warning: Could not save {format_type.upper()} report: {e}")

        if saved_files:
            print(f"📁 Total files saved: {len(saved_files)}")
            for file in saved_files:
                file_size = os.path.getsize(file) if os.path.exists(file) else 0
                print(f"   - {file} ({file_size:,} bytes)")

        return saved_files


def main():
    """Command line interface"""
    if len(sys.argv) < 2:
        print("Usage: python cape_simple_service.py <file_path> [options]")
        print("\nOptions:")
        print("  --url <url>          CAPEv2 URL (default: http://localhost:8000)")
        print("  --timeout <seconds>  Analysis timeout (default: 300)")
        print("  --max-wait <seconds> Max wait time (default: 1800)")
        print("  --output <file>      Save report to file")
        print("  --format <format>    Report format: json, html, lite (default: json)")
        print("  --machine <name>     Specific VM to use")
        print("  --package <name>     Analysis package")
        print("  --force              Force reanalysis even if existing results found")
        print("  --no-cache           Skip cache check, always perform new analysis")
        print("  --procmemdump <0|1>  Enable process memory dump")
        print("  --import-reconstruction <0|1>  Enable import reconstruction")
        print("  --unpacker <0|1|2>   Unpacker mode (0=off, 1=on, 2=advanced)")
        print("  --norefer <0|1>      Disable referrer")
        print("  --no-iat <0|1>       Disable IAT reconstruction")
        print("  --option <key=value> Custom analysis option")
        print("  --verbose <0|1>      Verbose output (1=detailed, 0=results only, default=1)")
        print("\nExamples:")
        print("  python cape_simple_service.py malware.exe")
        print("  python cape_simple_service.py malware.exe --output custom_report.json")
        print("  python cape_simple_service.py malware.exe --force  # Force reanalysis")
        print("  python cape_simple_service.py sample.pdf --machine Windows10 --package pdf")
        print("  python cape_simple_service.py malware.exe --procmemdump 1 --unpacker 2")
        print("  python cape_simple_service.py malware.exe --option 'procmemdump=1,unpacker=2'")
        print("\nNote: JSON and HTML summary reports are automatically saved as <file_hash>.json and <file_hash>.html")
        print("\nDefault analysis options (can be overridden):")
        print("  procmemdump=1, import_reconstruction=1, unpacker=2, norefer=1, no-iat=1")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    # Parse arguments
    cape_url = "http://localhost:8000"
    timeout = 300
    max_wait = 1800
    output_file = None
    report_format = 'json'
    force_reanalyze = False
    no_cache = False
    verbose = True
    options = {}
    
    i = 2
    while i < len(sys.argv):
        arg = sys.argv[i]
        if arg == '--url' and i + 1 < len(sys.argv):
            cape_url = sys.argv[i + 1]
            i += 2
        elif arg == '--timeout' and i + 1 < len(sys.argv):
            timeout = int(sys.argv[i + 1])
            i += 2
        elif arg == '--max-wait' and i + 1 < len(sys.argv):
            max_wait = int(sys.argv[i + 1])
            i += 2
        elif arg == '--output' and i + 1 < len(sys.argv):
            output_file = sys.argv[i + 1]
            i += 2
        elif arg == '--format' and i + 1 < len(sys.argv):
            report_format = sys.argv[i + 1]
            i += 2
        elif arg == '--machine' and i + 1 < len(sys.argv):
            options['machine'] = sys.argv[i + 1]
            i += 2
        elif arg == '--package' and i + 1 < len(sys.argv):
            options['package'] = sys.argv[i + 1]
            i += 2
        elif arg == '--force':
            force_reanalyze = True
            i += 1
        elif arg == '--no-cache':
            no_cache = True
            i += 1
        elif arg == '--procmemdump' and i + 1 < len(sys.argv):
            options['procmemdump'] = sys.argv[i + 1]
            i += 2
        elif arg == '--import-reconstruction' and i + 1 < len(sys.argv):
            options['import_reconstruction'] = sys.argv[i + 1]
            i += 2
        elif arg == '--unpacker' and i + 1 < len(sys.argv):
            options['unpacker'] = sys.argv[i + 1]
            i += 2
        elif arg == '--norefer' and i + 1 < len(sys.argv):
            options['norefer'] = sys.argv[i + 1]
            i += 2
        elif arg == '--no-iat' and i + 1 < len(sys.argv):
            options['no-iat'] = sys.argv[i + 1]
            i += 2
        elif arg == '--option' and i + 1 < len(sys.argv):
            # Parse custom options like "key=value" or "key1=value1,key2=value2"
            custom_options = sys.argv[i + 1]
            for opt in custom_options.split(','):
                if '=' in opt:
                    key, value = opt.split('=', 1)
                    options[key.strip()] = value.strip()
            i += 2
        elif arg == '--verbose' and i + 1 < len(sys.argv):
            verbose = sys.argv[i + 1] == '1'
            i += 2
        else:
            i += 1
    
    # Create service and analyze
    service = CAPEAnalysisService(cape_url, timeout, max_wait, verbose)
    result = service.analyze_file(file_path, options, output_file, report_format, force_reanalyze or no_cache)
    
    if result['error']:
        print(f"\n❌ Analysis failed: {result['message']}")
        if 'task_id' in result:
            print(f"Task ID: {result['task_id']}")
        sys.exit(1)
    else:
        print(f"\n✅ Analysis successful!")
        print(f"Task ID: {result['task_id']}")
        print(f"File MD5: {result['file_hash']}")
        if result.get('was_cached'):
            print("♻️  Results from existing analysis (cached)")
        else:
            print("🆕 Fresh analysis completed")

        print("\n📄 Report summary:")
        if isinstance(result['report'], dict):
            # Print basic info from report
            info = result['report'].get('info', {})
            target = result['report'].get('target', {})
            print(f"  File: {target.get('file', {}).get('name', 'Unknown')}")
            print(f"  Size: {target.get('file', {}).get('size', 'Unknown')} bytes")
            print(f"  MD5: {target.get('file', {}).get('md5', 'Unknown')}")
            print(f"  Analysis ID: {info.get('id', 'Unknown')}")
            print(f"  Duration: {info.get('duration', 'Unknown')} seconds")

            # Show detection info if available
            if 'detections' in result['report']:
                detections = result['report']['detections']
                print(f"  Detections: {len(detections)} found")

            # Show behavior summary if available
            if 'behavior' in result['report'] and 'summary' in result['report']['behavior']:
                summary = result['report']['behavior']['summary']
                if 'files' in summary:
                    print(f"  Files created: {len(summary['files'])}")
                if 'keys' in summary:
                    print(f"  Registry keys: {len(summary['keys'])}")
                if 'mutexes' in summary:
                    print(f"  Mutexes: {len(summary['mutexes'])}")


if __name__ == "__main__":
    main()
