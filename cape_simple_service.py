#!/usr/bin/env python3
"""
CAPEv2 Simple Analysis Service
Synchronous file analysis pipeline using existing CAPEv2 API
"""

import requests
import time
import json
import os
import sys
import hashlib
from pathlib import Path


class CAPEAnalysisService:
    def __init__(self, cape_url="http://localhost:8000", timeout=300, max_wait=1800):
        """
        Initialize CAPE analysis service
        
        Args:
            cape_url: Base URL of CAPEv2 web interface
            timeout: Analysis timeout in seconds
            max_wait: Maximum time to wait for completion
        """
        self.cape_url = cape_url.rstrip('/')
        self.timeout = timeout
        self.max_wait = max_wait
        self.session = requests.Session()
    
    def calculate_file_hash(self, file_path, hash_type='md5'):
        """
        Calculate file hash

        Args:
            file_path: Path to file
            hash_type: Type of hash (md5, sha1, sha256)

        Returns:
            Hash string or None if error
        """
        try:
            hash_func = getattr(hashlib, hash_type)()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            print(f"✗ Error calculating {hash_type}: {e}")
            return None

    def search_existing_analysis(self, file_hash):
        """
        Search for existing analysis by file hash with report availability check

        Args:
            file_hash: MD5 hash of file

        Returns:
            task_id if found with available report, None if not found
        """
        url = f"{self.cape_url}/apiv2/tasks/search/md5/{file_hash}/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error') and result.get('data'):
                    tasks = result['data']
                    if tasks:
                        # Check tasks from most recent to oldest
                        for task in sorted(tasks, key=lambda x: x.get('id', 0), reverse=True):
                            if task.get('status') in ['completed', 'reported']:
                                task_id = task['id']
                                print(f"✓ Found existing analysis: Task ID {task_id}")

                                # Verify report is actually available
                                if self._check_report_availability(task_id):
                                    print(f"✓ Report confirmed available for Task ID {task_id}")
                                    return task_id
                                else:
                                    print(f"⚠️  Report not available for Task ID {task_id}, checking next...")
                                    continue
            return None
        except Exception as e:
            print(f"⚠️  Warning: Could not search existing analysis: {e}")
            return None

    def _check_report_availability(self, task_id):
        """
        Check if report is actually available for a task

        Args:
            task_id: Task ID to check

        Returns:
            True if report is available, False otherwise
        """
        url = f"{self.cape_url}/apiv2/tasks/report/{task_id}/json/"

        try:
            response = self.session.get(url)
            return response.status_code == 200
        except:
            return False

    def submit_file(self, file_path, options=None):
        """
        Submit file for analysis

        Args:
            file_path: Path to file to analyze
            options: Dict of analysis options

        Returns:
            task_id or None if failed
        """
        url = f"{self.cape_url}/apiv2/tasks/create/file/"
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f)}
                data = {
                    'timeout': self.timeout,
                    'priority': 1
                }
                
                # Add custom options if provided
                if options:
                    data.update(options)
                
                response = self.session.post(url, files=files, data=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if not result.get('error') and result.get('data', {}).get('task_ids'):
                        task_id = result['data']['task_ids'][0]
                        print(f"✓ File submitted successfully. Task ID: {task_id}")
                        return task_id
                    else:
                        print(f"✗ Submission failed: {result.get('error_value', 'Unknown error')}")
                        return None
                else:
                    print(f"✗ HTTP Error {response.status_code}: {response.text}")
                    return None
                    
        except Exception as e:
            print(f"✗ Error submitting file: {e}")
            return None
    
    def check_status(self, task_id):
        """Check task status"""
        url = f"{self.cape_url}/apiv2/tasks/view/{task_id}/"
        
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    return result.get('data', {}).get('task', {}).get('status')
            return None
        except:
            return None
    
    def wait_for_completion(self, task_id):
        """
        Wait for analysis to complete
        
        Args:
            task_id: Task ID to wait for
            
        Returns:
            True if completed, False if timeout/error
        """
        print(f"⏳ Waiting for analysis completion (max {self.max_wait}s)...")
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < self.max_wait:
            status = self.check_status(task_id)
            
            if status != last_status:
                print(f"📊 Status: {status}")
                last_status = status
            
            if status in ['completed', 'reported']:
                print("✓ Analysis completed!")
                return True
            elif status in ['failed_analysis', 'failed_processing', 'failed_reporting']:
                print(f"✗ Analysis failed with status: {status}")
                return False
            
            time.sleep(5)  # Check every 5 seconds
        
        print("⏰ Timeout waiting for analysis completion")
        return False
    
    def get_report(self, task_id, format='json'):
        """
        Get analysis report with fallback options

        Args:
            task_id: Task ID
            format: Report format ('json', 'html', 'lite')

        Returns:
            Report data or None
        """
        # Try requested format first
        url = f"{self.cape_url}/apiv2/tasks/report/{task_id}/{format}/"

        try:
            response = self.session.get(url)
            if response.status_code == 200:
                if format == 'json' or format == 'lite':
                    return response.json()
                else:
                    return response.text
            elif response.status_code == 404:
                print(f"⚠️  {format.upper()} report not found, trying alternatives...")

                # Try alternative formats if requested format fails
                fallback_formats = ['lite', 'json'] if format not in ['lite', 'json'] else ['json', 'lite']

                for fallback_format in fallback_formats:
                    if fallback_format == format:
                        continue

                    fallback_url = f"{self.cape_url}/apiv2/tasks/report/{task_id}/"
                    try:
                        fallback_response = self.session.get(fallback_url)
                        if fallback_response.status_code == 200:
                            print(f"✓ Using {fallback_format.upper()} report instead")
                            if fallback_format == 'json' or fallback_format == 'lite':
                                return fallback_response.json()
                            else:
                                return fallback_response.text
                    except:
                        continue

                print(f"✗ No reports available for task {task_id}")
                return None
            else:
                print(f"✗ Error getting report: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"✗ Error getting report: {e}")
            return None
    
    def analyze_file(self, file_path, options=None, output_file=None, report_format='json', force_reanalyze=False):
        """
        Complete analysis pipeline: check existing → submit → wait → get results

        Args:
            file_path: Path to file to analyze
            options: Analysis options dict
            output_file: Path to save report (optional)
            report_format: Format of report ('json', 'html', 'lite')
            force_reanalyze: Force new analysis even if existing results found

        Returns:
            dict with results or error info
        """
        print(f"🔍 Starting analysis of: {file_path}")

        # Check if file exists
        if not os.path.exists(file_path):
            return {"error": True, "message": f"File not found: {file_path}"}

        # Calculate file hash
        print("🔢 Calculating file hash...")
        file_hash = self.calculate_file_hash(file_path, 'md5')
        if not file_hash:
            return {"error": True, "message": "Failed to calculate file hash"}

        print(f"📋 File MD5: {file_hash}")

        # Check for existing analysis (unless forced to reanalyze)
        task_id = None
        was_cached = False
        if not force_reanalyze:
            print("🔍 Checking for existing analysis...")
            existing_task_id = self.search_existing_analysis(file_hash)
            if existing_task_id:
                print("♻️  Using existing analysis results")
                task_id = existing_task_id
                was_cached = True

        # If no existing analysis found or forced reanalysis, submit new task
        if task_id is None:
            if force_reanalyze:
                print("🔄 Force reanalysis requested")
            else:
                print("🆕 No existing analysis found or reports unavailable")

            print("📤 Submitting file for new analysis...")
            task_id = self.submit_file(file_path, options)
            if not task_id:
                return {"error": True, "message": "Failed to submit file"}

            # Wait for completion
            if not self.wait_for_completion(task_id):
                return {"error": True, "message": "Analysis did not complete in time", "task_id": task_id}

        # Get report
        print("📄 Retrieving analysis report...")
        report = self.get_report(task_id, report_format)

        if report is None:
            return {"error": True, "message": "Failed to retrieve report", "task_id": task_id}

        # Save report if requested
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    if isinstance(report, dict):
                        json.dump(report, f, indent=2, ensure_ascii=False)
                    else:
                        f.write(report)
                print(f"💾 Report saved to: {output_file}")
            except Exception as e:
                print(f"⚠️  Warning: Could not save report to file: {e}")

        print("✅ Analysis completed successfully!")
        return {
            "error": False,
            "task_id": task_id,
            "file_hash": file_hash,
            "report": report,
            "message": "Analysis completed successfully",
            "was_cached": was_cached
        }


def main():
    """Command line interface"""
    if len(sys.argv) < 2:
        print("Usage: python cape_simple_service.py <file_path> [options]")
        print("\nOptions:")
        print("  --url <url>          CAPEv2 URL (default: http://localhost:8000)")
        print("  --timeout <seconds>  Analysis timeout (default: 300)")
        print("  --max-wait <seconds> Max wait time (default: 1800)")
        print("  --output <file>      Save report to file")
        print("  --format <format>    Report format: json, html, lite (default: json)")
        print("  --machine <name>     Specific VM to use")
        print("  --package <name>     Analysis package")
        print("  --force              Force reanalysis even if existing results found")
        print("  --no-cache           Skip cache check, always perform new analysis")
        print("\nExamples:")
        print("  python cape_simple_service.py malware.exe --output report.json")
        print("  python cape_simple_service.py malware.exe --force  # Force reanalysis")
        print("  python cape_simple_service.py sample.pdf --machine Windows10 --package pdf")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    # Parse arguments
    cape_url = "http://localhost:8000"
    timeout = 300
    max_wait = 1800
    output_file = None
    report_format = 'json'
    force_reanalyze = False
    no_cache = False
    options = {}
    
    i = 2
    while i < len(sys.argv):
        arg = sys.argv[i]
        if arg == '--url' and i + 1 < len(sys.argv):
            cape_url = sys.argv[i + 1]
            i += 2
        elif arg == '--timeout' and i + 1 < len(sys.argv):
            timeout = int(sys.argv[i + 1])
            i += 2
        elif arg == '--max-wait' and i + 1 < len(sys.argv):
            max_wait = int(sys.argv[i + 1])
            i += 2
        elif arg == '--output' and i + 1 < len(sys.argv):
            output_file = sys.argv[i + 1]
            i += 2
        elif arg == '--format' and i + 1 < len(sys.argv):
            report_format = sys.argv[i + 1]
            i += 2
        elif arg == '--machine' and i + 1 < len(sys.argv):
            options['machine'] = sys.argv[i + 1]
            i += 2
        elif arg == '--package' and i + 1 < len(sys.argv):
            options['package'] = sys.argv[i + 1]
            i += 2
        elif arg == '--force':
            force_reanalyze = True
            i += 1
        elif arg == '--no-cache':
            no_cache = True
            i += 1
        else:
            i += 1
    
    # Create service and analyze
    service = CAPEAnalysisService(cape_url, timeout, max_wait)
    result = service.analyze_file(file_path, options, output_file, report_format, force_reanalyze or no_cache)
    
    if result['error']:
        print(f"\n❌ Analysis failed: {result['message']}")
        if 'task_id' in result:
            print(f"Task ID: {result['task_id']}")
        sys.exit(1)
    else:
        print(f"\n✅ Analysis successful!")
        print(f"Task ID: {result['task_id']}")
        print(f"File MD5: {result['file_hash']}")
        if result.get('was_cached'):
            print("♻️  Results from existing analysis (cached)")
        else:
            print("🆕 Fresh analysis completed")

        if not output_file:
            print("\n📄 Report summary:")
            if isinstance(result['report'], dict):
                # Print basic info from report
                info = result['report'].get('info', {})
                target = result['report'].get('target', {})
                print(f"  File: {target.get('file', {}).get('name', 'Unknown')}")
                print(f"  Size: {target.get('file', {}).get('size', 'Unknown')} bytes")
                print(f"  MD5: {target.get('file', {}).get('md5', 'Unknown')}")
                print(f"  Analysis ID: {info.get('id', 'Unknown')}")
                print(f"  Duration: {info.get('duration', 'Unknown')} seconds")


if __name__ == "__main__":
    main()
