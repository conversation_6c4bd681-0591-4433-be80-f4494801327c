{% extends "base.html" %}

{% block title %}Upload File - CAPEv2 Web Service{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-upload"></i> File Analysis</h4>
                <p class="mb-0 text-muted">Upload a file for malware analysis using CAPEv2</p>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('upload_file') }}" enctype="multipart/form-data" id="uploadForm">
                    <!-- File Upload Area -->
                    <div class="upload-area mb-4" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>Drag & Drop files here or click to browse</h5>
                        <p class="text-muted">Supported formats: EXE, DLL, PDF, DOC, XLS, PPT, ZIP, APK, etc.</p>
                        <input type="file" name="file" id="fileInput" class="d-none" required>
                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open"></i> Browse Files
                        </button>
                    </div>

                    <!-- Selected File Display -->
                    <div id="selectedFile" class="alert alert-info d-none">
                        <i class="fas fa-file"></i> <span id="fileName"></span>
                        <button type="button" class="btn-close float-end" onclick="clearFile()"></button>
                    </div>

                    <!-- Analysis Mode Selection -->
                    <div class="mb-4">
                        <h6><i class="fas fa-cogs"></i> Analysis Mode</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysis_mode" id="basicMode" value="basic" checked>
                            <label class="form-check-label" for="basicMode">
                                <strong>Basic Analysis</strong> - Use default settings (recommended)
                                <br><small class="text-muted">procmemdump=1, import_reconstruction=1, unpacker=2, norefer=1, no-iat=1</small>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysis_mode" id="advancedMode" value="advanced">
                            <label class="form-check-label" for="advancedMode">
                                <strong>Advanced Analysis</strong> - Customize options
                            </label>
                        </div>
                    </div>

                    <!-- Advanced Options (Hidden by default) -->
                    <div id="advancedOptions" class="d-none">
                        <div class="option-group">
                            <h6><i class="fas fa-server"></i> VM & Package Settings</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="machine" class="form-label">Virtual Machine</label>
                                    <select name="machine" id="machine" class="form-select">
                                        <option value="">First available</option>
                                        <option value="Windows10">Windows 10</option>
                                        <option value="Windows7">Windows 7</option>
                                        <option value="Ubuntu">Ubuntu</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="package" class="form-label">Analysis Package</label>
                                    <select name="package" id="package" class="form-select">
                                        <option value="">Auto-detect</option>
                                        <option value="exe">Executable</option>
                                        <option value="dll">DLL</option>
                                        <option value="pdf">PDF</option>
                                        <option value="doc">Document</option>
                                        <option value="zip">Archive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="timeout" class="form-label">Timeout (seconds)</label>
                                    <input type="number" name="timeout" id="timeout" class="form-control" value="300" min="60" max="3600">
                                </div>
                                <div class="col-md-6">
                                    <label for="priority" class="form-label">Priority</label>
                                    <select name="priority" id="priority" class="form-select">
                                        <option value="1">Normal</option>
                                        <option value="2">High</option>
                                        <option value="3">Critical</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="option-group">
                            <h6><i class="fas fa-tools"></i> Analysis Options</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="process_memory" id="process_memory" checked>
                                        <label class="form-check-label" for="process_memory">Process Memory Dump</label>
                                    </div>
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="import_reconstruction" id="import_reconstruction" checked>
                                        <label class="form-check-label" for="import_reconstruction">Import Reconstruction</label>
                                    </div>
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="unpacker" id="unpacker" checked>
                                        <label class="form-check-label" for="unpacker">Advanced Unpacker</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="norefer" id="norefer" checked>
                                        <label class="form-check-label" for="norefer">Disable Referrer</label>
                                    </div>
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="no_iat" id="no_iat" checked>
                                        <label class="form-check-label" for="no_iat">Disable IAT Reconstruction</label>
                                    </div>
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="free" id="free">
                                        <label class="form-check-label" for="free">Free Analysis</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="nohuman" id="nohuman">
                                        <label class="form-check-label" for="nohuman">No Human Interaction</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check analysis-option">
                                        <input class="form-check-input" type="checkbox" name="tor" id="tor">
                                        <label class="form-check-label" for="tor">Use Tor Network</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="option-group">
                            <h6><i class="fas fa-code"></i> Custom Options</h6>
                            <label for="custom_options" class="form-label">Additional Options (comma-separated)</label>
                            <input type="text" name="custom_options" id="custom_options" class="form-control" 
                                   placeholder="e.g., option1=value1,option2=value2">
                            <small class="form-text text-muted">Format: key=value,key2=value2</small>
                        </div>
                    </div>

                    <!-- Verbose Option -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="verbose" id="verbose" value="1">
                            <label class="form-check-label" for="verbose">
                                Verbose Output (show detailed progress)
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-play"></i> Start Analysis
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// File upload handling
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const selectedFile = document.getElementById('selectedFile');
const fileName = document.getElementById('fileName');

// Drag and drop functionality
uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        showSelectedFile(files[0].name);
    }
});

uploadArea.addEventListener('click', () => {
    fileInput.click();
});

fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        showSelectedFile(e.target.files[0].name);
    }
});

function showSelectedFile(name) {
    fileName.textContent = name;
    selectedFile.classList.remove('d-none');
    uploadArea.style.display = 'none';
}

function clearFile() {
    fileInput.value = '';
    selectedFile.classList.add('d-none');
    uploadArea.style.display = 'block';
}

// Analysis mode toggle
document.getElementById('basicMode').addEventListener('change', function() {
    if (this.checked) {
        document.getElementById('advancedOptions').classList.add('d-none');
    }
});

document.getElementById('advancedMode').addEventListener('change', function() {
    if (this.checked) {
        document.getElementById('advancedOptions').classList.remove('d-none');
    }
});

// Form submission
document.getElementById('uploadForm').addEventListener('submit', function() {
    document.getElementById('submitBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
    document.getElementById('submitBtn').disabled = true;
});
</script>
{% endblock %}
